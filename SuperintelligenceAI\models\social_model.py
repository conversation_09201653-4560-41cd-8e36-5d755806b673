import numpy as np
from typing import Dict, Any, Optional, List, Tuple
import logging
import time
from collections import defaultdict

logger = logging.getLogger(__name__)

class SocialDynamicsModel:
    """
    Production-grade social dynamics model with advanced relationship modeling.
    """

    def __init__(self):
        self.social_graph = {}
        self.relationship_strength = {}
        self.interaction_history = []
        self.social_roles = {}
        self.trust_levels = {}
        self.influence_network = {}
        self.social_context = {}
        self.group_dynamics = {}

    def add_interaction(self, entity1: str, entity2: str, interaction_type: str,
                       strength: float = 1.0, context: Optional[Dict[str, Any]] = None) -> None:
        """Add interaction with strength and context."""
        if entity1 not in self.social_graph:
            self.social_graph[entity1] = {}

        self.social_graph[entity1][entity2] = {
            'type': interaction_type,
            'strength': strength,
            'timestamp': time.time(),
            'context': context or {}
        }

        # Update relationship strength
        relationship_key = tuple(sorted([entity1, entity2]))
        if relationship_key not in self.relationship_strength:
            self.relationship_strength[relationship_key] = 0.0

        # Positive interactions increase strength, negative decrease
        strength_modifier = 0.1 if self._is_positive_interaction(interaction_type) else -0.05
        self.relationship_strength[relationship_key] += strength_modifier * strength
        self.relationship_strength[relationship_key] = max(-1.0, min(1.0,
            self.relationship_strength[relationship_key]))

        # Add to interaction history
        self.interaction_history.append({
            'entity1': entity1,
            'entity2': entity2,
            'type': interaction_type,
            'strength': strength,
            'timestamp': time.time(),
            'context': context or {}
        })

        # Keep history manageable
        if len(self.interaction_history) > 10000:
            self.interaction_history = self.interaction_history[-5000:]

        logger.debug(f"Added interaction: {entity1} -> {entity2} ({interaction_type})")

    def predict_interaction(self, entity1: str, entity2: str) -> Dict[str, Any]:
        """Predict interaction with confidence and reasoning."""
        if entity1 not in self.social_graph or entity2 not in self.social_graph[entity1]:
            return {
                'predicted_type': 'unknown',
                'confidence': 0.0,
                'reasoning': 'No previous interactions'
            }

        interaction_data = self.social_graph[entity1][entity2]
        relationship_key = tuple(sorted([entity1, entity2]))
        relationship_strength = self.relationship_strength.get(relationship_key, 0.0)

        # Calculate prediction confidence based on interaction history
        similar_interactions = self._find_similar_interactions(entity1, entity2)
        confidence = min(1.0, len(similar_interactions) * 0.1 + abs(relationship_strength))

        return {
            'predicted_type': interaction_data['type'],
            'confidence': confidence,
            'relationship_strength': relationship_strength,
            'last_interaction': interaction_data['timestamp'],
            'reasoning': f'Based on {len(similar_interactions)} similar interactions'
        }

    def update_trust_level(self, entity1: str, entity2: str, trust_change: float) -> None:
        """Update trust level between entities."""
        trust_key = (entity1, entity2)
        if trust_key not in self.trust_levels:
            self.trust_levels[trust_key] = 0.5  # Neutral trust

        self.trust_levels[trust_key] += trust_change
        self.trust_levels[trust_key] = max(0.0, min(1.0, self.trust_levels[trust_key]))

    def get_trust_level(self, entity1: str, entity2: str) -> float:
        """Get trust level between entities."""
        trust_key = (entity1, entity2)
        return self.trust_levels.get(trust_key, 0.5)

    def assign_social_role(self, entity: str, role: str, competence: float = 1.0) -> None:
        """Assign social role to an entity."""
        if entity not in self.social_roles:
            self.social_roles[entity] = {}
        self.social_roles[entity][role] = competence

    def get_social_influence(self, entity: str) -> float:
        """Calculate social influence of an entity."""
        if entity not in self.social_graph:
            return 0.0

        # Calculate influence based on connections and relationship strengths
        total_influence = 0.0
        connections = len(self.social_graph[entity])

        for connected_entity in self.social_graph[entity]:
            relationship_key = tuple(sorted([entity, connected_entity]))
            strength = self.relationship_strength.get(relationship_key, 0.0)
            total_influence += max(0, strength)  # Only positive relationships contribute

        # Normalize by number of connections
        return total_influence / max(1, connections)

    def detect_social_clusters(self) -> Dict[str, List[str]]:
        """Detect social clusters/communities in the network."""
        clusters = {}
        visited = set()
        cluster_id = 0

        for entity in self.social_graph:
            if entity not in visited:
                cluster = self._dfs_cluster(entity, visited, threshold=0.3)
                if len(cluster) > 1:
                    clusters[f'cluster_{cluster_id}'] = cluster
                    cluster_id += 1

        return clusters

    def predict_group_behavior(self, group: List[str], scenario: str) -> Dict[str, Any]:
        """Predict how a group might behave in a given scenario."""
        if not group:
            return {'prediction': 'no_action', 'confidence': 0.0}

        # Analyze group dynamics
        avg_influence = np.mean([self.get_social_influence(entity) for entity in group])
        group_cohesion = self._calculate_group_cohesion(group)

        # Simple prediction based on group characteristics
        if group_cohesion > 0.7 and avg_influence > 0.5:
            prediction = 'coordinated_action'
            confidence = 0.8
        elif group_cohesion > 0.5:
            prediction = 'moderate_cooperation'
            confidence = 0.6
        else:
            prediction = 'individual_actions'
            confidence = 0.4

        return {
            'prediction': prediction,
            'confidence': confidence,
            'group_cohesion': group_cohesion,
            'average_influence': avg_influence,
            'group_size': len(group)
        }

    def _is_positive_interaction(self, interaction_type: str) -> bool:
        """Determine if an interaction type is positive."""
        positive_types = ['cooperation', 'help', 'support', 'friendship', 'collaboration']
        return interaction_type.lower() in positive_types

    def _find_similar_interactions(self, entity1: str, entity2: str) -> List[Dict[str, Any]]:
        """Find similar interactions in history."""
        similar = []
        for interaction in self.interaction_history:
            if ((interaction['entity1'] == entity1 and interaction['entity2'] == entity2) or
                (interaction['entity1'] == entity2 and interaction['entity2'] == entity1)):
                similar.append(interaction)
        return similar

    def _dfs_cluster(self, entity: str, visited: set, threshold: float) -> List[str]:
        """Depth-first search for clustering."""
        visited.add(entity)
        cluster = [entity]

        if entity in self.social_graph:
            for connected_entity in self.social_graph[entity]:
                if connected_entity not in visited:
                    relationship_key = tuple(sorted([entity, connected_entity]))
                    strength = self.relationship_strength.get(relationship_key, 0.0)
                    if strength >= threshold:
                        cluster.extend(self._dfs_cluster(connected_entity, visited, threshold))

        return cluster

    def _calculate_group_cohesion(self, group: List[str]) -> float:
        """Calculate cohesion within a group."""
        if len(group) < 2:
            return 1.0

        total_strength = 0.0
        pair_count = 0

        for i, entity1 in enumerate(group):
            for entity2 in group[i+1:]:
                relationship_key = tuple(sorted([entity1, entity2]))
                strength = self.relationship_strength.get(relationship_key, 0.0)
                total_strength += max(0, strength)  # Only positive relationships
                pair_count += 1

        return total_strength / max(1, pair_count)

    def get_social_network_stats(self) -> Dict[str, Any]:
        """Get statistics about the social network."""
        total_entities = len(self.social_graph)
        total_relationships = sum(len(connections) for connections in self.social_graph.values())

        if total_entities == 0:
            return {'total_entities': 0, 'total_relationships': 0, 'network_density': 0.0}

        network_density = total_relationships / (total_entities * (total_entities - 1))
        avg_relationship_strength = np.mean(list(self.relationship_strength.values())) if self.relationship_strength else 0.0

        return {
            'total_entities': total_entities,
            'total_relationships': total_relationships,
            'network_density': network_density,
            'average_relationship_strength': avg_relationship_strength,
            'total_interactions': len(self.interaction_history),
            'clusters': len(self.detect_social_clusters())
        }

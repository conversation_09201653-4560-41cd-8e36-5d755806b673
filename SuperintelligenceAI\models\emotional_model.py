import numpy as np
from typing import Dict, Any, Optional, List, Tuple
import logging
import time

logger = logging.getLogger(__name__)

class EmotionalIntelligenceModel:
    """
    Production-grade emotional intelligence model with advanced emotional processing.
    """

    def __init__(self):
        self.emotional_state = {}
        self.emotional_history = []
        self.emotional_weights = {
            'happiness': 1.0, 'sadness': -0.5, 'anger': -0.8, 'fear': -0.6,
            'surprise': 0.3, 'disgust': -0.4, 'trust': 0.8, 'anticipation': 0.6
        }
        self.emotional_decay_rate = 0.95
        self.emotional_intensity = {}
        self.emotional_triggers = {}
        self.empathy_model = {}

    def update_emotional_state(self, event: str, emotion: str, intensity: float = 1.0) -> None:
        """Update emotional state with intensity and timestamp."""
        self.emotional_state[event] = {
            'emotion': emotion,
            'intensity': intensity,
            'timestamp': time.time()
        }

        # Add to emotional history
        self.emotional_history.append({
            'event': event,
            'emotion': emotion,
            'intensity': intensity,
            'timestamp': time.time()
        })

        # Keep history manageable
        if len(self.emotional_history) > 1000:
            self.emotional_history = self.emotional_history[-500:]

        logger.debug(f"Updated emotional state: {event} -> {emotion} (intensity: {intensity})")

    def get_emotional_reaction(self, event: str) -> Dict[str, Any]:
        """Get emotional reaction with decay and context."""
        if event not in self.emotional_state:
            return {"emotion": "neutral", "intensity": 0.0, "confidence": 0.0}

        emotion_data = self.emotional_state[event]
        current_time = time.time()
        time_diff = current_time - emotion_data['timestamp']

        # Apply emotional decay
        decay_factor = self.emotional_decay_rate ** (time_diff / 3600)  # Hourly decay
        current_intensity = emotion_data['intensity'] * decay_factor

        # Calculate emotional weight
        emotion_weight = self.emotional_weights.get(emotion_data['emotion'], 0.0)

        return {
            'emotion': emotion_data['emotion'],
            'intensity': current_intensity,
            'weight': emotion_weight,
            'confidence': min(1.0, current_intensity * abs(emotion_weight)),
            'decay_factor': decay_factor
        }

    def process_emotional_context(self, context: Dict[str, Any]) -> Dict[str, float]:
        """Process emotional context and return emotional scores."""
        emotional_scores = {}

        for emotion in self.emotional_weights.keys():
            score = 0.0

            # Analyze context for emotional cues
            if 'positive_indicators' in context:
                if emotion in ['happiness', 'trust', 'anticipation']:
                    score += len(context['positive_indicators']) * 0.2

            if 'negative_indicators' in context:
                if emotion in ['sadness', 'anger', 'fear', 'disgust']:
                    score += len(context['negative_indicators']) * 0.2

            emotional_scores[emotion] = min(1.0, score)

        return emotional_scores

    def learn_emotional_pattern(self, trigger: str, emotional_response: str,
                              strength: float = 1.0) -> None:
        """Learn emotional patterns from experiences."""
        if trigger not in self.emotional_triggers:
            self.emotional_triggers[trigger] = {}

        if emotional_response not in self.emotional_triggers[trigger]:
            self.emotional_triggers[trigger][emotional_response] = 0.0

        self.emotional_triggers[trigger][emotional_response] += strength * 0.1
        self.emotional_triggers[trigger][emotional_response] = min(1.0,
            self.emotional_triggers[trigger][emotional_response])

    def predict_emotional_response(self, trigger: str) -> Optional[Dict[str, float]]:
        """Predict emotional response based on learned patterns."""
        if trigger not in self.emotional_triggers:
            return None

        return self.emotional_triggers[trigger].copy()

    def emotional_regulation(self, target_emotion: str, regulation_strength: float = 0.5) -> None:
        """Regulate emotions towards a target state."""
        for event, emotion_data in self.emotional_state.items():
            current_emotion = emotion_data['emotion']
            current_intensity = emotion_data['intensity']

            # Apply regulation
            if current_emotion != target_emotion:
                # Reduce intensity of non-target emotions
                new_intensity = current_intensity * (1 - regulation_strength * 0.1)
                self.emotional_state[event]['intensity'] = max(0.0, new_intensity)

    def empathy_response(self, other_emotional_state: Dict[str, Any]) -> Dict[str, float]:
        """Generate empathetic response to another entity's emotional state."""
        empathy_scores = {}

        for emotion, weight in self.emotional_weights.items():
            if emotion in other_emotional_state:
                # Mirror emotional state with empathy factor
                empathy_factor = 0.7  # How much we mirror others' emotions
                empathy_scores[emotion] = other_emotional_state[emotion] * empathy_factor

        return empathy_scores

    def get_emotional_summary(self) -> Dict[str, Any]:
        """Get summary of current emotional state."""
        if not self.emotional_history:
            return {"dominant_emotion": "neutral", "average_intensity": 0.0, "emotional_stability": 1.0}

        recent_emotions = self.emotional_history[-10:]  # Last 10 emotions

        # Calculate dominant emotion
        emotion_counts = {}
        total_intensity = 0.0

        for emotion_entry in recent_emotions:
            emotion = emotion_entry['emotion']
            intensity = emotion_entry['intensity']

            emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
            total_intensity += intensity

        dominant_emotion = max(emotion_counts.items(), key=lambda x: x[1])[0] if emotion_counts else "neutral"
        average_intensity = total_intensity / len(recent_emotions)

        # Calculate emotional stability (lower variance = more stable)
        intensities = [e['intensity'] for e in recent_emotions]
        emotional_stability = 1.0 / (1.0 + np.var(intensities)) if len(intensities) > 1 else 1.0

        return {
            "dominant_emotion": dominant_emotion,
            "average_intensity": average_intensity,
            "emotional_stability": emotional_stability,
            "emotion_distribution": emotion_counts,
            "total_emotional_events": len(self.emotional_history)
        }

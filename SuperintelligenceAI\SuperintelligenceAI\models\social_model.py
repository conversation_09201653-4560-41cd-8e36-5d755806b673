class SocialDynamicsModel:
    def __init__(self):
        self.social_graph = {}

    def add_interaction(self, entity1, entity2, interaction_type):
        if entity1 not in self.social_graph:
            self.social_graph[entity1] = {}
        self.social_graph[entity1][entity2] = interaction_type

    def predict_interaction(self, entity1, entity2):
        return self.social_graph.get(entity1, {}).get(entity2, "unknown")
